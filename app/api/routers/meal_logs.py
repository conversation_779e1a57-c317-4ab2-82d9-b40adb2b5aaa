from typing import Annotated

from fastapi import APIRouter
from fastapi import Depends
from fastapi import File
from fastapi import HTTPException
from fastapi import UploadFile
from fastapi import status

from app.api.schemas.meal_logs import MealLogRequest
from app.api.schemas.meal_logs import MealLogResponse
from app.api.services.meal_logs import MealLogService

router = APIRouter()


@router.post("/meal_logs/", response_model=MealLogResponse)
async def create_meal_log(
    meal_log_data: Annotated[MealLogRequest, Depends(MealLogRequest.as_form)],
    image_file: UploadFile = File(...),
) -> MealLogResponse:
    try:
        result = await MealLogService.create_meal_log(
            timestamp=meal_log_data.timestamp,
            total_weight_g=meal_log_data.total_weight_g,
            delta_weight_g=meal_log_data.delta_weight_g,
            device_id=meal_log_data.device_id,
            image_file=image_file,
        )

        return MealLogResponse(**result)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create meal log: {e!s}",
        )


@router.get("/meal_logs/health")
async def meal_logs_health():
    return {"status": "healthy", "service": "meal_logs"}
