"""
FastAPI application for Smart Plate Backend.

This module provides the main FastAPI application instance and configuration.
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi

from config.fastapi import settings

from .middleware.cors import get_cors_config
from .routers import auth
from .routers import health
from .routers import meal_logs
from .routers import users

# Create FastAPI app instance
app = FastAPI(
    title=settings.PROJECT_NAME,
    description=settings.DESCRIPTION,
    version=settings.VERSION,
    docs_url=settings.DOCS_URL,
    redoc_url=settings.REDOC_URL,
)

# Add CORS middleware
app.add_middleware(CORSMiddleware, **get_cors_config())

# Include routers
app.include_router(health.router, prefix="/v1", tags=["health"])
app.include_router(auth.router, prefix="/v1/auth", tags=["authentication"])
app.include_router(users.router, prefix="/v1/users", tags=["users"])
# app.include_router(meal_logs.router, prefix="/v1", tags=["meal_logs"])


def custom_openapi():
    """Custom OpenAPI schema configuration."""
    if app.openapi_schema:
        return app.openapi_schema

    openapi_schema = get_openapi(
        title=settings.PROJECT_NAME + " API",
        version=settings.VERSION,
        description=settings.DESCRIPTION,
        routes=app.routes,
    )
    openapi_schema["servers"] = [{"url": "/api"}]
    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": f"Welcome to {settings.PROJECT_NAME} API",
        "docs": settings.DOCS_URL,
        "redoc": settings.REDOC_URL,
    }
