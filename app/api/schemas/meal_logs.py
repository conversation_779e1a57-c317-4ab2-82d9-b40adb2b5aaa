from datetime import datetime

from fastapi import Form
from pydantic import BaseModel


class AISuggestion(BaseModel):
    label: str
    confidence: float


class FoodEntryResponse(BaseModel):
    id: str
    label: str
    weight_g: float
    estimated_kcal: float
    protein_g: float
    carbs_g: float
    fat_g: float
    fibre_g: float
    manual_override: bool
    source: str


class SessionTotals(BaseModel):
    total_kcal: float
    protein_g: float
    carbs_g: float
    fat_g: float
    fibre_g: float


class MealLogResponse(BaseModel):
    meal_log_id: str
    meal_session_id: str
    status: str
    image_url: str
    ai_suggestions: list[AISuggestion]
    entries: list[FoodEntryResponse]
    session_totals: SessionTotals


class MealLogRequest(BaseModel):
    timestamp: datetime
    total_weight_g: float
    delta_weight_g: float
    device_id: str

    @classmethod
    def as_form(
        cls,
        timestamp: str = Form(...),
        total_weight_g: float = Form(...),
        delta_weight_g: float = Form(...),
        device_id: str = Form(...),
    ) -> "MealLogRequest":
        return cls(
            timestamp=datetime.fromisoformat(timestamp.replace("Z", "+00:00")),
            total_weight_g=total_weight_g,
            delta_weight_g=delta_weight_g,
            device_id=device_id,
        )
